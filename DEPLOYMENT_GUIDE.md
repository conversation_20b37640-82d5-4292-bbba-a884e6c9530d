# Spring Boot API - Deployment Guide

## Overview

This guide covers deploying the Spring Boot API in various environments with security best practices.

## Prerequisites

- Java 11 or higher
- Maven 3.6 or higher
- Docker (for containerized deployment)
- PostgreSQL (for production)

## Environment Configurations

### Development Environment

1. **Clone and Build**
   ```bash
   git clone <repository-url>
   cd spring-boot-api
   mvn clean install
   ```

2. **Run Application**
   ```bash
   mvn spring-boot:run
   ```

3. **Access Application**
   - API: http://localhost:8080/api
   - Swagger UI: http://localhost:8080/api/swagger-ui.html
   - H2 Console: http://localhost:8080/api/h2-console

### Production Environment

#### 1. Database Setup (PostgreSQL)

```sql
-- Create database and user
CREATE DATABASE springbootapi;
CREATE USER apiuser WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE springbootapi TO apiuser;

-- Create tables (run with production profile)
-- Tables will be created automatically with Hibernate
```

#### 2. Environment Variables

Create a `.env` file or set environment variables:

```bash
# Database Configuration
DATABASE_URL=**********************************************
DATABASE_USERNAME=apiuser
DATABASE_PASSWORD=secure_password

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here-minimum-256-bits
JWT_EXPIRATION=3600000

# Server Configuration
SERVER_PORT=8080

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# Logging
LOG_LEVEL=INFO
```

#### 3. Production Build

```bash
# Build with production profile
mvn clean package -Pprod -DskipTests

# Run with production profile
java -jar target/spring-boot-api-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

## Docker Deployment

### 1. Build Docker Image

```bash
# Build image
docker build -t spring-boot-api:latest .

# Or use docker-compose
docker-compose build
```

### 2. Run with Docker Compose

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f spring-boot-api

# Stop services
docker-compose down
```

### 3. Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  spring-boot-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DATABASE_URL=*********************************************
      - DATABASE_USERNAME=apiuser
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRATION=3600000
    depends_on:
      - postgres
    networks:
      - spring-boot-network
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: springbootapi
      POSTGRES_USER: apiuser
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - spring-boot-network
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - spring-boot-api
    networks:
      - spring-boot-network
    restart: unless-stopped

networks:
  spring-boot-network:
    driver: bridge

volumes:
  postgres_data:
```

## Kubernetes Deployment

### 1. Create Namespace

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: spring-boot-api
```

### 2. ConfigMap and Secrets

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: spring-boot-api-config
  namespace: spring-boot-api
data:
  SPRING_PROFILES_ACTIVE: "prod"
  SERVER_PORT: "8080"
  JWT_EXPIRATION: "3600000"

---
# secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: spring-boot-api-secrets
  namespace: spring-boot-api
type: Opaque
data:
  DATABASE_URL: <base64-encoded-url>
  DATABASE_USERNAME: <base64-encoded-username>
  DATABASE_PASSWORD: <base64-encoded-password>
  JWT_SECRET: <base64-encoded-secret>
```

### 3. Deployment

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: spring-boot-api
  namespace: spring-boot-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: spring-boot-api
  template:
    metadata:
      labels:
        app: spring-boot-api
    spec:
      containers:
      - name: spring-boot-api
        image: spring-boot-api:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: spring-boot-api-config
        - secretRef:
            name: spring-boot-api-secrets
        livenessProbe:
          httpGet:
            path: /api/actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

### 4. Service and Ingress

```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: spring-boot-api-service
  namespace: spring-boot-api
spec:
  selector:
    app: spring-boot-api
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP

---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: spring-boot-api-ingress
  namespace: spring-boot-api
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.yourdomain.com
    secretName: spring-boot-api-tls
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: spring-boot-api-service
            port:
              number: 80
```

## Cloud Deployment

### AWS ECS

1. **Create Task Definition**
2. **Set up Application Load Balancer**
3. **Configure Auto Scaling**
4. **Set up RDS for PostgreSQL**

### Google Cloud Run

```bash
# Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT_ID/spring-boot-api

# Deploy to Cloud Run
gcloud run deploy spring-boot-api \
  --image gcr.io/PROJECT_ID/spring-boot-api \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars SPRING_PROFILES_ACTIVE=prod
```

### Azure Container Instances

```bash
# Create resource group
az group create --name spring-boot-api-rg --location eastus

# Deploy container
az container create \
  --resource-group spring-boot-api-rg \
  --name spring-boot-api \
  --image spring-boot-api:latest \
  --dns-name-label spring-boot-api \
  --ports 8080 \
  --environment-variables SPRING_PROFILES_ACTIVE=prod
```

## Security Considerations

### 1. SSL/TLS Configuration

```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    location / {
        proxy_pass http://spring-boot-api:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. Firewall Rules

```bash
# Allow only necessary ports
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 8080/tcp   # Block direct access to app
```

### 3. Database Security

- Use strong passwords
- Enable SSL connections
- Restrict network access
- Regular backups
- Monitor access logs

## Monitoring and Logging

### 1. Application Monitoring

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'spring-boot-api'
    static_configs:
      - targets: ['spring-boot-api:8080']
    metrics_path: '/api/actuator/prometheus'
```

### 2. Log Aggregation

```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /app/logs/*.log
  fields:
    service: spring-boot-api
  fields_under_root: true

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
```

## Backup and Recovery

### 1. Database Backup

```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U apiuser springbootapi > $BACKUP_DIR/backup_$DATE.sql
```

### 2. Application State

- Configuration files
- SSL certificates
- Log files
- Application data

## Performance Optimization

### 1. JVM Tuning

```bash
java -Xms512m -Xmx1g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -jar spring-boot-api.jar
```

### 2. Database Optimization

- Connection pooling
- Query optimization
- Index creation
- Regular maintenance

### 3. Caching

- Redis for session storage
- Application-level caching
- CDN for static content

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check connection string
   - Verify credentials
   - Test network connectivity

2. **JWT Token Issues**
   - Verify secret key
   - Check token expiration
   - Validate token format

3. **Permission Errors**
   - Check user roles
   - Verify endpoint permissions
   - Review security configuration

### Health Checks

```bash
# Application health
curl http://localhost:8080/api/actuator/health

# Database connectivity
curl http://localhost:8080/api/actuator/health/db

# Custom health indicators
curl http://localhost:8080/api/actuator/health/custom
```
