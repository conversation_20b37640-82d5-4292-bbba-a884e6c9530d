spring:
  application:
    name: spring-boot-api-test
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: false
  
  h2:
    console:
      enabled: false

server:
  port: 0

app:
  jwt:
    secret: testSecretKey123456789012345678901234567890
    expiration: 86400000
  security:
    cors:
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"
      allow-credentials: false

logging:
  level:
    com.example.springbootapi: DEBUG
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
