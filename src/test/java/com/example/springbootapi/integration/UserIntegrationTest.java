package com.example.springbootapi.integration;

import com.example.springbootapi.dto.request.CreateUserRequest;
import com.example.springbootapi.dto.request.LoginRequest;
import com.example.springbootapi.model.enums.UserType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class UserIntegrationTest {

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();
    }

    private String getAuthToken() throws Exception {
        // Create a user first
        CreateUserRequest signupRequest = new CreateUserRequest();
        signupRequest.setUsername("testadmin");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPassword("password123");
        signupRequest.setFirstName("Test");
        signupRequest.setLastName("Admin");
        signupRequest.setUserType(UserType.CORPORATE);
        signupRequest.setCompanyName("Test Company");

        mockMvc.perform(post("/auth/signup")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signupRequest)))
                .andExpect(status().isOk());

        // Login to get token
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsername("testadmin");
        loginRequest.setPassword("password123");

        MvcResult result = mockMvc.perform(post("/auth/signin")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseContent);
        return jsonNode.get("accessToken").asText();
    }

    @Test
    public void testCreateUserWithAuthentication() throws Exception {
        String token = getAuthToken();

        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("newuser");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        request.setFirstName("New");
        request.setLastName("User");
        request.setUserType(UserType.AGENT);
        request.setAgentId("AGT001");
        request.setAgencyName("Test Agency");

        mockMvc.perform(post("/users")
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.username").value("newuser"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.userType").value("AGENT"));
    }

    @Test
    public void testGetUsersWithAuthentication() throws Exception {
        String token = getAuthToken();

        mockMvc.perform(get("/users")
                .header("Authorization", "Bearer " + token)
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.totalElements").exists());
    }

    @Test
    public void testGetUsersWithoutAuthentication() throws Exception {
        mockMvc.perform(get("/users"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    public void testSearchUsers() throws Exception {
        String token = getAuthToken();

        mockMvc.perform(get("/users/search")
                .header("Authorization", "Bearer " + token)
                .param("query", "test")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    public void testGetUsersByType() throws Exception {
        String token = getAuthToken();

        mockMvc.perform(get("/users/type/CORPORATE")
                .header("Authorization", "Bearer " + token)
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    public void testUpdateUser() throws Exception {
        String token = getAuthToken();

        // First create a user
        CreateUserRequest createRequest = new CreateUserRequest();
        createRequest.setUsername("updateuser");
        createRequest.setEmail("<EMAIL>");
        createRequest.setPassword("password123");
        createRequest.setFirstName("Update");
        createRequest.setLastName("User");
        createRequest.setUserType(UserType.CORPORATE);
        createRequest.setCompanyName("Update Company");

        MvcResult createResult = mockMvc.perform(post("/users")
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isOk())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        JsonNode createJsonNode = objectMapper.readTree(createResponseContent);
        Long userId = createJsonNode.get("id").asLong();

        // Update the user
        CreateUserRequest updateRequest = new CreateUserRequest();
        updateRequest.setUsername("updateuser");
        updateRequest.setEmail("<EMAIL>");
        updateRequest.setFirstName("Updated");
        updateRequest.setLastName("User");
        updateRequest.setUserType(UserType.CORPORATE);
        updateRequest.setCompanyName("Updated Company");

        mockMvc.perform(put("/users/" + userId)
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.firstName").value("Updated"))
                .andExpect(jsonPath("$.companyName").value("Updated Company"));
    }

    @Test
    public void testDeactivateUser() throws Exception {
        String token = getAuthToken();

        // First create a user
        CreateUserRequest createRequest = new CreateUserRequest();
        createRequest.setUsername("deactivateuser");
        createRequest.setEmail("<EMAIL>");
        createRequest.setPassword("password123");
        createRequest.setFirstName("Deactivate");
        createRequest.setLastName("User");
        createRequest.setUserType(UserType.CORPORATE);

        MvcResult createResult = mockMvc.perform(post("/users")
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isOk())
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        JsonNode createJsonNode = objectMapper.readTree(createResponseContent);
        Long userId = createJsonNode.get("id").asLong();

        // Deactivate the user
        mockMvc.perform(put("/users/" + userId + "/deactivate")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("User deactivated successfully!"));
    }
}
