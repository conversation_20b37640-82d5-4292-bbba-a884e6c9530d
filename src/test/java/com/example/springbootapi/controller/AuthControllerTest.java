package com.example.springbootapi.controller;

import com.example.springbootapi.dto.request.CreateUserRequest;
import com.example.springbootapi.dto.request.LoginRequest;
import com.example.springbootapi.model.enums.UserType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class AuthControllerTest {

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    public void setup() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();
    }

    @Test
    public void testSignupSuccess() throws Exception {
        setup();
        
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("testuser");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        request.setFirstName("Test");
        request.setLastName("User");
        request.setUserType(UserType.CORPORATE);
        request.setCompanyName("Test Company");

        mockMvc.perform(post("/auth/signup")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("User registered successfully!"));
    }

    @Test
    public void testSignupDuplicateUsername() throws Exception {
        setup();
        
        // First user
        CreateUserRequest request1 = new CreateUserRequest();
        request1.setUsername("testuser");
        request1.setEmail("<EMAIL>");
        request1.setPassword("password123");
        request1.setFirstName("Test");
        request1.setLastName("User");
        request1.setUserType(UserType.CORPORATE);

        mockMvc.perform(post("/auth/signup")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isOk());

        // Second user with same username
        CreateUserRequest request2 = new CreateUserRequest();
        request2.setUsername("testuser");
        request2.setEmail("<EMAIL>");
        request2.setPassword("password123");
        request2.setFirstName("Test");
        request2.setLastName("User2");
        request2.setUserType(UserType.AGENT);

        mockMvc.perform(post("/auth/signup")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Error: Username is already taken!"));
    }

    @Test
    public void testSigninSuccess() throws Exception {
        setup();
        
        // First create a user
        CreateUserRequest signupRequest = new CreateUserRequest();
        signupRequest.setUsername("testuser");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPassword("password123");
        signupRequest.setFirstName("Test");
        signupRequest.setLastName("User");
        signupRequest.setUserType(UserType.CORPORATE);

        mockMvc.perform(post("/auth/signup")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signupRequest)))
                .andExpect(status().isOk());

        // Then sign in
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("password123");

        mockMvc.perform(post("/auth/signin")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").exists())
                .andExpect(jsonPath("$.username").value("testuser"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
    }

    @Test
    public void testSigninInvalidCredentials() throws Exception {
        setup();
        
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsername("nonexistent");
        loginRequest.setPassword("wrongpassword");

        mockMvc.perform(post("/auth/signin")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized());
    }
}
