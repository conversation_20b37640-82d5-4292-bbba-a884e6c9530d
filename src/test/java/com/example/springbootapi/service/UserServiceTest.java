package com.example.springbootapi.service;

import com.example.springbootapi.dto.request.CreateUserRequest;
import com.example.springbootapi.model.CorporateUser;
import com.example.springbootapi.model.User;
import com.example.springbootapi.model.enums.UserType;
import com.example.springbootapi.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    private UserService userService;

    private CreateUserRequest createUserRequest;
    private CorporateUser corporateUser;

    @BeforeEach
    void setUp() {
        createUserRequest = new CreateUserRequest();
        createUserRequest.setUsername("testuser");
        createUserRequest.setEmail("<EMAIL>");
        createUserRequest.setPassword("password123");
        createUserRequest.setFirstName("Test");
        createUserRequest.setLastName("User");
        createUserRequest.setUserType(UserType.CORPORATE);
        createUserRequest.setCompanyName("Test Company");
        createUserRequest.setDepartment("IT");
        createUserRequest.setJobTitle("Developer");
        createUserRequest.setEmployeeId("EMP001");

        corporateUser = new CorporateUser(
            "testuser",
            "<EMAIL>",
            "encodedPassword",
            "Test",
            "User",
            "Test Company",
            "IT",
            "Developer",
            "EMP001"
        );
        corporateUser.setId(1L);
    }

    @Test
    void testCreateUserSuccess() {
        // Arrange
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(userRepository.save(any(User.class))).thenReturn(corporateUser);

        // Act
        User result = userService.createUser(createUserRequest);

        // Assert
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        assertEquals("<EMAIL>", result.getEmail());
        assertEquals(UserType.CORPORATE, result.getUserType());
        
        verify(userRepository).existsByUsername("testuser");
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(passwordEncoder).encode("password123");
        verify(userRepository).save(any(User.class));
    }

    @Test
    void testCreateUserDuplicateUsername() {
        // Arrange
        when(userRepository.existsByUsername(anyString())).thenReturn(true);

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> userService.createUser(createUserRequest));
        
        assertEquals("Error: Username is already taken!", exception.getMessage());
        verify(userRepository).existsByUsername("testuser");
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void testCreateUserDuplicateEmail() {
        // Arrange
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(true);

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> userService.createUser(createUserRequest));
        
        assertEquals("Error: Email is already in use!", exception.getMessage());
        verify(userRepository).existsByUsername("testuser");
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void testFindByIdSuccess() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(corporateUser));

        // Act
        Optional<User> result = userService.findById(1L);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(corporateUser, result.get());
        verify(userRepository).findById(1L);
    }

    @Test
    void testFindByIdNotFound() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.empty());

        // Act
        Optional<User> result = userService.findById(1L);

        // Assert
        assertFalse(result.isPresent());
        verify(userRepository).findById(1L);
    }

    @Test
    void testDeactivateUserSuccess() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(corporateUser));
        when(userRepository.save(any(User.class))).thenReturn(corporateUser);

        // Act
        userService.deactivateUser(1L);

        // Assert
        assertFalse(corporateUser.getActive());
        verify(userRepository).findById(1L);
        verify(userRepository).save(corporateUser);
    }

    @Test
    void testDeactivateUserNotFound() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> userService.deactivateUser(1L));
        
        assertEquals("User not found with id: 1", exception.getMessage());
        verify(userRepository).findById(1L);
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void testDeleteUserSuccess() {
        // Arrange
        when(userRepository.existsById(1L)).thenReturn(true);

        // Act
        userService.deleteUser(1L);

        // Assert
        verify(userRepository).existsById(1L);
        verify(userRepository).deleteById(1L);
    }

    @Test
    void testDeleteUserNotFound() {
        // Arrange
        when(userRepository.existsById(1L)).thenReturn(false);

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> userService.deleteUser(1L));
        
        assertEquals("User not found with id: 1", exception.getMessage());
        verify(userRepository).existsById(1L);
        verify(userRepository, never()).deleteById(any());
    }
}
