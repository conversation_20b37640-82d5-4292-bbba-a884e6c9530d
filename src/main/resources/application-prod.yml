spring:
  application:
    name: spring-boot-api
  
  datasource:
    url: ${DATABASE_URL:**********************************************}
    username: ${DATABASE_USERNAME:apiuser}
    password: ${DATABASE_PASSWORD:apipassword}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  h2:
    console:
      enabled: false

server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api
  error:
    include-message: never
    include-binding-errors: never
    include-stacktrace: never
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.example.springbootapi: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.springframework.web: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/spring-boot-api.log
    max-size: 10MB
    max-history: 30

app:
  jwt:
    secret: ${JWT_SECRET:productionSecretKey123456789012345678901234567890}
    expiration: ${JWT_EXPIRATION:3600000} # 1 hour in production
  security:
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS:https://yourdomain.com}
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true
    rate-limit:
      enabled: true
      requests-per-minute: 100
