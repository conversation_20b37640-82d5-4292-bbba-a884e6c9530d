package com.example.springbootapi.service;

import com.example.springbootapi.dto.request.CreateUserGroupRequest;
import com.example.springbootapi.model.User;
import com.example.springbootapi.model.UserGroup;
import com.example.springbootapi.repository.UserGroupRepository;
import com.example.springbootapi.repository.UserRepository;
import com.example.springbootapi.security.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UserGroupService {

    @Autowired
    private UserGroupRepository userGroupRepository;

    @Autowired
    private UserRepository userRepository;

    public UserGroup createUserGroup(CreateUserGroupRequest request) {
        // Validate unique constraints
        if (userGroupRepository.existsByName(request.getName())) {
            throw new RuntimeException("Error: Group name is already taken!");
        }

        // Get current user ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Long currentUserId = userDetails.getId();

        UserGroup userGroup = new UserGroup(request.getName(), request.getDescription(), currentUserId);
        return userGroupRepository.save(userGroup);
    }

    public Optional<UserGroup> findById(Long id) {
        return userGroupRepository.findById(id);
    }

    public Optional<UserGroup> findByName(String name) {
        return userGroupRepository.findByName(name);
    }

    public Page<UserGroup> findAllActiveGroups(Pageable pageable) {
        return userGroupRepository.findByActiveTrue(pageable);
    }

    public Page<UserGroup> searchGroups(String searchTerm, Pageable pageable) {
        return userGroupRepository.searchActiveGroups(searchTerm, pageable);
    }

    public List<UserGroup> findGroupsByUserId(Long userId) {
        return userGroupRepository.findActiveGroupsByUserId(userId);
    }

    public List<UserGroup> findGroupsByCompanyId(Long companyId) {
        return userGroupRepository.findActiveGroupsByCompanyId(companyId);
    }

    public UserGroup updateUserGroup(Long id, CreateUserGroupRequest request) {
        UserGroup userGroup = userGroupRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("UserGroup not found with id: " + id));

        // Check if name is being changed and if it conflicts
        if (!userGroup.getName().equals(request.getName()) && 
            userGroupRepository.existsByName(request.getName())) {
            throw new RuntimeException("Error: Group name is already taken!");
        }

        userGroup.setName(request.getName());
        userGroup.setDescription(request.getDescription());

        return userGroupRepository.save(userGroup);
    }

    public void deactivateUserGroup(Long id) {
        UserGroup userGroup = userGroupRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("UserGroup not found with id: " + id));
        userGroup.setActive(false);
        userGroupRepository.save(userGroup);
    }

    public void activateUserGroup(Long id) {
        UserGroup userGroup = userGroupRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("UserGroup not found with id: " + id));
        userGroup.setActive(true);
        userGroupRepository.save(userGroup);
    }

    public void deleteUserGroup(Long id) {
        if (!userGroupRepository.existsById(id)) {
            throw new RuntimeException("UserGroup not found with id: " + id);
        }
        userGroupRepository.deleteById(id);
    }

    public UserGroup addUserToGroup(Long groupId, Long userId) {
        UserGroup userGroup = userGroupRepository.findById(groupId)
            .orElseThrow(() -> new RuntimeException("UserGroup not found with id: " + groupId));
        
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        if (!user.getActive()) {
            throw new RuntimeException("Cannot add inactive user to group");
        }

        if (userGroup.getUsers().contains(user)) {
            throw new RuntimeException("User is already a member of this group");
        }

        userGroup.addUser(user);
        return userGroupRepository.save(userGroup);
    }

    public UserGroup removeUserFromGroup(Long groupId, Long userId) {
        UserGroup userGroup = userGroupRepository.findById(groupId)
            .orElseThrow(() -> new RuntimeException("UserGroup not found with id: " + groupId));
        
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        if (!userGroup.getUsers().contains(user)) {
            throw new RuntimeException("User is not a member of this group");
        }

        userGroup.removeUser(user);
        return userGroupRepository.save(userGroup);
    }
}
