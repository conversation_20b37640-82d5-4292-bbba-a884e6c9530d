package com.example.springbootapi.service;

import com.example.springbootapi.dto.request.CreateUserRequest;
import com.example.springbootapi.model.*;
import com.example.springbootapi.model.enums.UserType;
import com.example.springbootapi.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    public User createUser(CreateUserRequest request) {
        // Validate unique constraints
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("Error: Username is already taken!");
        }

        if (userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("Error: Email is already in use!");
        }

        // Create user based on type
        User user = createUserByType(request);
        user.setPassword(passwordEncoder.encode(request.getPassword()));

        return userRepository.save(user);
    }

    private User createUserByType(CreateUserRequest request) {
        User user;
        switch (request.getUserType()) {
            case CORPORATE:
                CorporateUser corporateUser = new CorporateUser(
                    request.getUsername(),
                    request.getEmail(),
                    request.getPassword(),
                    request.getFirstName(),
                    request.getLastName(),
                    request.getCompanyName(),
                    request.getDepartment(),
                    request.getJobTitle(),
                    request.getEmployeeId()
                );
                corporateUser.setPhoneNumber(request.getPhoneNumber());
                user = corporateUser;
                break;
            case AGENT:
                AgentUser agentUser = new AgentUser(
                    request.getUsername(),
                    request.getEmail(),
                    request.getPassword(),
                    request.getFirstName(),
                    request.getLastName(),
                    request.getAgentId(),
                    request.getAgencyName(),
                    request.getLicenseNumber(),
                    request.getLicenseExpiryDate()
                );
                agentUser.setPhoneNumber(request.getPhoneNumber());
                agentUser.setCommissionRate(request.getCommissionRate());
                agentUser.setSpecialization(request.getSpecialization());
                user = agentUser;
                break;
            case ASSOCIATE:
                AssociateUser associateUser = new AssociateUser(
                    request.getUsername(),
                    request.getEmail(),
                    request.getPassword(),
                    request.getFirstName(),
                    request.getLastName(),
                    request.getAssociateId(),
                    request.getSupervisorId(),
                    request.getCertification(),
                    request.getHireDate()
                );
                associateUser.setPhoneNumber(request.getPhoneNumber());
                associateUser.setCertificationExpiryDate(request.getCertificationExpiryDate());
                associateUser.setLevel(request.getLevel());
                associateUser.setTrainingStatus(request.getTrainingStatus());
                user = associateUser;
                break;
            default:
                throw new RuntimeException("Invalid user type: " + request.getUserType());
        }
        return user;
    }

    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    public Page<User> findAllActiveUsers(Pageable pageable) {
        return userRepository.findByActiveTrue(pageable);
    }

    public Page<User> findUsersByType(UserType userType, Pageable pageable) {
        return userRepository.findByUserTypeAndActiveTrue(userType, pageable);
    }

    public Page<User> searchUsers(String searchTerm, Pageable pageable) {
        return userRepository.searchActiveUsers(searchTerm, pageable);
    }

    public List<User> findUsersByGroupId(Long groupId) {
        return userRepository.findActiveUsersByGroupId(groupId);
    }

    public User updateUser(Long id, CreateUserRequest request) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("User not found with id: " + id));

        // Check if username/email is being changed and if it conflicts
        if (!user.getUsername().equals(request.getUsername()) && 
            userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("Error: Username is already taken!");
        }

        if (!user.getEmail().equals(request.getEmail()) && 
            userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("Error: Email is already in use!");
        }

        // Update common fields
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setPhoneNumber(request.getPhoneNumber());

        // Update password if provided
        if (request.getPassword() != null && !request.getPassword().isEmpty()) {
            user.setPassword(passwordEncoder.encode(request.getPassword()));
        }

        return userRepository.save(user);
    }

    public void deactivateUser(Long id) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("User not found with id: " + id));
        user.setActive(false);
        userRepository.save(user);
    }

    public void activateUser(Long id) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("User not found with id: " + id));
        user.setActive(true);
        userRepository.save(user);
    }

    public void deleteUser(Long id) {
        if (!userRepository.existsById(id)) {
            throw new RuntimeException("User not found with id: " + id);
        }
        userRepository.deleteById(id);
    }
}
