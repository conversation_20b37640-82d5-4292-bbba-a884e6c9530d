package com.example.springbootapi.service;

import com.example.springbootapi.dto.request.CreateCompanyRequest;
import com.example.springbootapi.model.Company;
import com.example.springbootapi.model.UserGroup;
import com.example.springbootapi.repository.CompanyRepository;
import com.example.springbootapi.repository.UserGroupRepository;
import com.example.springbootapi.security.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CompanyService {

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private UserGroupRepository userGroupRepository;

    public Company createCompany(CreateCompanyRequest request) {
        // Validate unique constraints
        if (companyRepository.existsByName(request.getName())) {
            throw new RuntimeException("Error: Company name is already taken!");
        }

        if (request.getRegistrationNumber() != null && 
            companyRepository.existsByRegistrationNumber(request.getRegistrationNumber())) {
            throw new RuntimeException("Error: Registration number is already in use!");
        }

        // Get current user ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Long currentUserId = userDetails.getId();

        Company company = new Company(
            request.getName(),
            request.getDescription(),
            request.getRegistrationNumber(),
            request.getIndustry(),
            currentUserId
        );

        // Set additional fields
        company.setAddress(request.getAddress());
        company.setCity(request.getCity());
        company.setState(request.getState());
        company.setPostalCode(request.getPostalCode());
        company.setCountry(request.getCountry());
        company.setPhoneNumber(request.getPhoneNumber());
        company.setEmail(request.getEmail());
        company.setWebsite(request.getWebsite());

        return companyRepository.save(company);
    }

    public Optional<Company> findById(Long id) {
        return companyRepository.findById(id);
    }

    public Optional<Company> findByName(String name) {
        return companyRepository.findByName(name);
    }

    public Optional<Company> findByRegistrationNumber(String registrationNumber) {
        return companyRepository.findByRegistrationNumber(registrationNumber);
    }

    public Page<Company> findAllActiveCompanies(Pageable pageable) {
        return companyRepository.findByActiveTrue(pageable);
    }

    public Page<Company> searchCompanies(String searchTerm, Pageable pageable) {
        return companyRepository.searchActiveCompanies(searchTerm, pageable);
    }

    public Page<Company> findCompaniesByIndustry(String industry, Pageable pageable) {
        return companyRepository.findByIndustryAndActiveTrue(industry, pageable);
    }

    public List<Company> findCompaniesByGroupId(Long groupId) {
        return companyRepository.findActiveCompaniesByGroupId(groupId);
    }

    public Company updateCompany(Long id, CreateCompanyRequest request) {
        Company company = companyRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Company not found with id: " + id));

        // Check if name is being changed and if it conflicts
        if (!company.getName().equals(request.getName()) && 
            companyRepository.existsByName(request.getName())) {
            throw new RuntimeException("Error: Company name is already taken!");
        }

        // Check if registration number is being changed and if it conflicts
        if (request.getRegistrationNumber() != null && 
            !request.getRegistrationNumber().equals(company.getRegistrationNumber()) &&
            companyRepository.existsByRegistrationNumber(request.getRegistrationNumber())) {
            throw new RuntimeException("Error: Registration number is already in use!");
        }

        // Update fields
        company.setName(request.getName());
        company.setDescription(request.getDescription());
        company.setRegistrationNumber(request.getRegistrationNumber());
        company.setIndustry(request.getIndustry());
        company.setAddress(request.getAddress());
        company.setCity(request.getCity());
        company.setState(request.getState());
        company.setPostalCode(request.getPostalCode());
        company.setCountry(request.getCountry());
        company.setPhoneNumber(request.getPhoneNumber());
        company.setEmail(request.getEmail());
        company.setWebsite(request.getWebsite());

        return companyRepository.save(company);
    }

    public void deactivateCompany(Long id) {
        Company company = companyRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Company not found with id: " + id));
        company.setActive(false);
        companyRepository.save(company);
    }

    public void activateCompany(Long id) {
        Company company = companyRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Company not found with id: " + id));
        company.setActive(true);
        companyRepository.save(company);
    }

    public void deleteCompany(Long id) {
        if (!companyRepository.existsById(id)) {
            throw new RuntimeException("Company not found with id: " + id);
        }
        companyRepository.deleteById(id);
    }

    public Company addUserGroupToCompany(Long companyId, Long groupId) {
        Company company = companyRepository.findById(companyId)
            .orElseThrow(() -> new RuntimeException("Company not found with id: " + companyId));
        
        UserGroup userGroup = userGroupRepository.findById(groupId)
            .orElseThrow(() -> new RuntimeException("UserGroup not found with id: " + groupId));

        if (!userGroup.getActive()) {
            throw new RuntimeException("Cannot associate inactive group with company");
        }

        if (company.getUserGroups().contains(userGroup)) {
            throw new RuntimeException("Group is already associated with this company");
        }

        company.addUserGroup(userGroup);
        return companyRepository.save(company);
    }

    public Company removeUserGroupFromCompany(Long companyId, Long groupId) {
        Company company = companyRepository.findById(companyId)
            .orElseThrow(() -> new RuntimeException("Company not found with id: " + companyId));
        
        UserGroup userGroup = userGroupRepository.findById(groupId)
            .orElseThrow(() -> new RuntimeException("UserGroup not found with id: " + groupId));

        if (!company.getUserGroups().contains(userGroup)) {
            throw new RuntimeException("Group is not associated with this company");
        }

        company.removeUserGroup(userGroup);
        return companyRepository.save(company);
    }
}
