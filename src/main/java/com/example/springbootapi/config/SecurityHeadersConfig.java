package com.example.springbootapi.config;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
public class SecurityHeadersConfig {

    @Bean
    public Filter securityHeadersFilter() {
        return new SecurityHeadersFilter();
    }

    public static class SecurityHeadersFilter implements Filter {
        @Override
        public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
                throws IOException, ServletException {
            
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            
            // Content Security Policy
            httpResponse.setHeader("Content-Security-Policy", 
                "default-src 'self'; " +
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                "style-src 'self' 'unsafe-inline'; " +
                "img-src 'self' data: https:; " +
                "font-src 'self' https:; " +
                "connect-src 'self'; " +
                "frame-ancestors 'none'");
            
            // X-Content-Type-Options
            httpResponse.setHeader("X-Content-Type-Options", "nosniff");
            
            // X-Frame-Options
            httpResponse.setHeader("X-Frame-Options", "DENY");
            
            // X-XSS-Protection
            httpResponse.setHeader("X-XSS-Protection", "1; mode=block");
            
            // Referrer Policy
            httpResponse.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
            
            // Permissions Policy
            httpResponse.setHeader("Permissions-Policy", 
                "camera=(), microphone=(), geolocation=(), payment=()");
            
            // Cache Control for sensitive endpoints
            String requestURI = ((javax.servlet.http.HttpServletRequest) request).getRequestURI();
            if (requestURI.contains("/auth/") || requestURI.contains("/users/") || 
                requestURI.contains("/groups/") || requestURI.contains("/companies/")) {
                httpResponse.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                httpResponse.setHeader("Pragma", "no-cache");
                httpResponse.setHeader("Expires", "0");
            }
            
            chain.doFilter(request, response);
        }
    }
}
