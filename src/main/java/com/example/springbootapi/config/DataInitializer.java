package com.example.springbootapi.config;

import com.example.springbootapi.model.*;
import com.example.springbootapi.model.enums.Role;
import com.example.springbootapi.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Set;

@Component
public class DataInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserGroupRepository userGroupRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        if (userRepository.count() == 0) {
            logger.info("Initializing default data...");
            initializeDefaultData();
            logger.info("Default data initialization completed.");
        }
    }

    private void initializeDefaultData() {
        // Create admin user
        CorporateUser admin = new CorporateUser(
            "admin",
            "<EMAIL>",
            passwordEncoder.encode("admin123"),
            "System",
            "Administrator",
            "Example Corp",
            "IT",
            "System Administrator",
            "EMP001"
        );
        admin.setRoles(Set.of(Role.ROLE_ADMIN, Role.ROLE_CORPORATE_USER));
        userRepository.save(admin);

        // Create group manager
        CorporateUser groupManager = new CorporateUser(
            "groupmgr",
            "<EMAIL>",
            passwordEncoder.encode("password123"),
            "Group",
            "Manager",
            "Example Corp",
            "HR",
            "Group Manager",
            "EMP002"
        );
        groupManager.setRoles(Set.of(Role.ROLE_GROUP_MANAGER, Role.ROLE_CORPORATE_USER));
        userRepository.save(groupManager);

        // Create company manager
        CorporateUser companyManager = new CorporateUser(
            "companymgr",
            "<EMAIL>",
            passwordEncoder.encode("password123"),
            "Company",
            "Manager",
            "Example Corp",
            "Business",
            "Company Manager",
            "EMP003"
        );
        companyManager.setRoles(Set.of(Role.ROLE_COMPANY_MANAGER, Role.ROLE_CORPORATE_USER));
        userRepository.save(companyManager);

        // Create sample agent user
        AgentUser agent = new AgentUser(
            "agent1",
            "<EMAIL>",
            passwordEncoder.encode("password123"),
            "John",
            "Agent",
            "AGT001",
            "Premium Agency",
            "LIC123456",
            LocalDate.now().plusYears(2)
        );
        userRepository.save(agent);

        // Create sample associate user
        AssociateUser associate = new AssociateUser(
            "associate1",
            "<EMAIL>",
            passwordEncoder.encode("password123"),
            "Jane",
            "Associate",
            "ASC001",
            admin.getId(),
            "Basic Certification",
            LocalDate.now().minusMonths(6)
        );
        userRepository.save(associate);

        // Create sample user groups
        UserGroup salesGroup = new UserGroup("Sales Team", "Sales department group", admin.getId());
        userGroupRepository.save(salesGroup);

        UserGroup supportGroup = new UserGroup("Support Team", "Customer support group", admin.getId());
        userGroupRepository.save(supportGroup);

        // Create sample companies
        Company company1 = new Company(
            "Tech Solutions Inc",
            "Leading technology solutions provider",
            "REG001",
            "Technology",
            admin.getId()
        );
        company1.setCity("New York");
        company1.setState("NY");
        company1.setCountry("USA");
        company1.setEmail("<EMAIL>");
        companyRepository.save(company1);

        Company company2 = new Company(
            "Global Services Ltd",
            "International business services",
            "REG002",
            "Services",
            admin.getId()
        );
        company2.setCity("London");
        company2.setCountry("UK");
        company2.setEmail("<EMAIL>");
        companyRepository.save(company2);

        // Add users to groups
        salesGroup.addUser(agent);
        salesGroup.addUser(groupManager);
        userGroupRepository.save(salesGroup);

        supportGroup.addUser(associate);
        supportGroup.addUser(companyManager);
        userGroupRepository.save(supportGroup);

        // Associate groups with companies
        company1.addUserGroup(salesGroup);
        company1.addUserGroup(supportGroup);
        companyRepository.save(company1);

        company2.addUserGroup(supportGroup);
        companyRepository.save(company2);

        logger.info("Created default users:");
        logger.info("- Admin: admin/admin123");
        logger.info("- Group Manager: groupmgr/password123");
        logger.info("- Company Manager: companymgr/password123");
        logger.info("- Agent: agent1/password123");
        logger.info("- Associate: associate1/password123");
    }
}
