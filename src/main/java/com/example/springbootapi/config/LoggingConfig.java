package com.example.springbootapi.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

@Configuration
public class LoggingConfig {

    @Bean
    public RequestLoggingFilter requestLoggingFilter() {
        return new RequestLoggingFilter();
    }

    public static class RequestLoggingFilter extends OncePerRequestFilter {
        private static final Logger logger = LoggerFactory.getLogger(RequestLoggingFilter.class);

        @Override
        protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                      FilterChain filterChain) throws ServletException, IOException {
            
            String requestId = UUID.randomUUID().toString().substring(0, 8);
            long startTime = System.currentTimeMillis();
            
            // Add request ID to response headers for tracking
            response.setHeader("X-Request-ID", requestId);
            
            try {
                // Log request
                logRequest(request, requestId);
                
                filterChain.doFilter(request, response);
                
                // Log response
                logResponse(request, response, requestId, startTime);
                
            } catch (Exception e) {
                // Log error
                logError(request, requestId, startTime, e);
                throw e;
            }
        }

        private void logRequest(HttpServletRequest request, String requestId) {
            String clientIp = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            String method = request.getMethod();
            String uri = request.getRequestURI();
            String queryString = request.getQueryString();
            
            logger.info("REQUEST [{}] {} {} {} - IP: {} - User-Agent: {}", 
                requestId, method, uri, 
                queryString != null ? "?" + queryString : "",
                clientIp, userAgent);
        }

        private void logResponse(HttpServletRequest request, HttpServletResponse response, 
                               String requestId, long startTime) {
            long duration = System.currentTimeMillis() - startTime;
            int status = response.getStatus();
            String method = request.getMethod();
            String uri = request.getRequestURI();
            
            if (status >= 400) {
                logger.warn("RESPONSE [{}] {} {} - Status: {} - Duration: {}ms", 
                    requestId, method, uri, status, duration);
            } else {
                logger.info("RESPONSE [{}] {} {} - Status: {} - Duration: {}ms", 
                    requestId, method, uri, status, duration);
            }
        }

        private void logError(HttpServletRequest request, String requestId, long startTime, Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            String method = request.getMethod();
            String uri = request.getRequestURI();
            
            logger.error("ERROR [{}] {} {} - Duration: {}ms - Error: {}", 
                requestId, method, uri, duration, e.getMessage(), e);
        }

        private String getClientIpAddress(HttpServletRequest request) {
            String xForwardedFor = request.getHeader("X-Forwarded-For");
            if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
                return xForwardedFor.split(",")[0].trim();
            }
            
            String xRealIp = request.getHeader("X-Real-IP");
            if (xRealIp != null && !xRealIp.isEmpty()) {
                return xRealIp;
            }
            
            return request.getRemoteAddr();
        }

        @Override
        protected boolean shouldNotFilter(HttpServletRequest request) {
            String path = request.getRequestURI();
            // Don't log health checks and static resources
            return path.contains("/actuator/health") || 
                   path.contains("/swagger-ui") || 
                   path.contains("/v3/api-docs");
        }
    }
}
