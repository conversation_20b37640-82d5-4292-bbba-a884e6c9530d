package com.example.springbootapi.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
public class RateLimitConfig {

    @Bean
    public RateLimitFilter rateLimitFilter() {
        return new RateLimitFilter();
    }

    public static class RateLimitFilter extends OncePerRequestFilter {
        private final ConcurrentHashMap<String, AtomicInteger> requestCounts = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<String, Long> requestTimes = new ConcurrentHashMap<>();
        private final int maxRequestsPerMinute = 100;
        private final long timeWindowMs = 60000; // 1 minute

        @Override
        protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                      FilterChain filterChain) throws ServletException, IOException {
            
            String clientIp = getClientIpAddress(request);
            long currentTime = System.currentTimeMillis();
            
            // Clean up old entries
            cleanupOldEntries(currentTime);
            
            // Check rate limit
            if (isRateLimited(clientIp, currentTime)) {
                response.setStatus(HttpServletResponse.SC_TOO_MANY_REQUESTS);
                response.setContentType("application/json");
                response.getWriter().write("{\"error\":\"Rate limit exceeded. Please try again later.\"}");
                return;
            }
            
            filterChain.doFilter(request, response);
        }

        private String getClientIpAddress(HttpServletRequest request) {
            String xForwardedFor = request.getHeader("X-Forwarded-For");
            if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
                return xForwardedFor.split(",")[0].trim();
            }
            
            String xRealIp = request.getHeader("X-Real-IP");
            if (xRealIp != null && !xRealIp.isEmpty()) {
                return xRealIp;
            }
            
            return request.getRemoteAddr();
        }

        private boolean isRateLimited(String clientIp, long currentTime) {
            Long lastRequestTime = requestTimes.get(clientIp);
            
            if (lastRequestTime == null || (currentTime - lastRequestTime) > timeWindowMs) {
                // Reset counter for new time window
                requestCounts.put(clientIp, new AtomicInteger(1));
                requestTimes.put(clientIp, currentTime);
                return false;
            }
            
            AtomicInteger count = requestCounts.get(clientIp);
            if (count == null) {
                count = new AtomicInteger(0);
                requestCounts.put(clientIp, count);
            }
            
            return count.incrementAndGet() > maxRequestsPerMinute;
        }

        private void cleanupOldEntries(long currentTime) {
            requestTimes.entrySet().removeIf(entry -> 
                (currentTime - entry.getValue()) > timeWindowMs);
            
            requestCounts.entrySet().removeIf(entry -> 
                !requestTimes.containsKey(entry.getKey()));
        }
    }
}
