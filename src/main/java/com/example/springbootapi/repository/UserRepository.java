package com.example.springbootapi.repository;

import com.example.springbootapi.model.User;
import com.example.springbootapi.model.enums.UserType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    Optional<User> findByUsername(String username);
    
    Optional<User> findByEmail(String email);
    
    Boolean existsByUsername(String username);
    
    Boolean existsByEmail(String email);
    
    List<User> findByUserType(UserType userType);
    
    List<User> findByActiveTrue();
    
    List<User> findByActiveFalse();
    
    @Query("SELECT u FROM User u WHERE u.active = true AND " +
           "(LOWER(u.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.username) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<User> searchActiveUsers(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Query("SELECT u FROM User u WHERE u.userType = :userType AND u.active = true")
    Page<User> findByUserTypeAndActiveTrue(@Param("userType") UserType userType, Pageable pageable);
    
    @Query("SELECT u FROM User u WHERE u.active = true")
    Page<User> findByActiveTrue(Pageable pageable);
    
    @Query("SELECT u FROM User u JOIN u.userGroups ug WHERE ug.id = :groupId AND u.active = true")
    List<User> findActiveUsersByGroupId(@Param("groupId") Long groupId);
}
