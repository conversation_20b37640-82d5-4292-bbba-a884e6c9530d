package com.example.springbootapi.repository;

import com.example.springbootapi.model.UserGroup;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserGroupRepository extends JpaRepository<UserGroup, Long> {
    
    Optional<UserGroup> findByName(String name);
    
    Boolean existsByName(String name);
    
    List<UserGroup> findByActiveTrue();
    
    List<UserGroup> findByActiveFalse();
    
    List<UserGroup> findByCreatedBy(Long createdBy);
    
    @Query("SELECT ug FROM UserGroup ug WHERE ug.active = true AND " +
           "(LOWER(ug.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(ug.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<UserGroup> searchActiveGroups(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Query("SELECT ug FROM UserGroup ug WHERE ug.active = true")
    Page<UserGroup> findByActiveTrue(Pageable pageable);
    
    @Query("SELECT ug FROM UserGroup ug JOIN ug.users u WHERE u.id = :userId AND ug.active = true")
    List<UserGroup> findActiveGroupsByUserId(@Param("userId") Long userId);
    
    @Query("SELECT ug FROM UserGroup ug JOIN ug.companies c WHERE c.id = :companyId AND ug.active = true")
    List<UserGroup> findActiveGroupsByCompanyId(@Param("companyId") Long companyId);
}
