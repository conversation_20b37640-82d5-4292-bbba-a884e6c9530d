package com.example.springbootapi.repository;

import com.example.springbootapi.model.Company;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CompanyRepository extends JpaRepository<Company, Long> {
    
    Optional<Company> findByName(String name);
    
    Optional<Company> findByRegistrationNumber(String registrationNumber);
    
    Boolean existsByName(String name);
    
    Boolean existsByRegistrationNumber(String registrationNumber);
    
    List<Company> findByActiveTrue();
    
    List<Company> findByActiveFalse();
    
    List<Company> findByIndustry(String industry);
    
    List<Company> findByCreatedBy(Long createdBy);
    
    @Query("SELECT c FROM Company c WHERE c.active = true AND " +
           "(LOWER(c.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.industry) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.registrationNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Company> searchActiveCompanies(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Query("SELECT c FROM Company c WHERE c.active = true")
    Page<Company> findByActiveTrue(Pageable pageable);
    
    @Query("SELECT c FROM Company c WHERE c.industry = :industry AND c.active = true")
    Page<Company> findByIndustryAndActiveTrue(@Param("industry") String industry, Pageable pageable);
    
    @Query("SELECT c FROM Company c JOIN c.userGroups ug WHERE ug.id = :groupId AND c.active = true")
    List<Company> findActiveCompaniesByGroupId(@Param("groupId") Long groupId);
}
