package com.example.springbootapi.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public class CreateUserGroupRequest {
    
    @NotBlank
    @Size(max = 100)
    private String name;

    @Size(max = 500)
    private String description;

    // Constructors
    public CreateUserGroupRequest() {}

    public CreateUserGroupRequest(String name, String description) {
        this.name = name;
        this.description = description;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
