package com.example.springbootapi.controller;

import com.example.springbootapi.dto.request.CreateUserGroupRequest;
import com.example.springbootapi.dto.response.MessageResponse;
import com.example.springbootapi.model.UserGroup;
import com.example.springbootapi.service.UserGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/groups")
@Tag(name = "User Group Management", description = "User group management APIs")
@SecurityRequirement(name = "bearerAuth")
public class UserGroupController {

    @Autowired
    private UserGroupService userGroupService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Get all groups", description = "Retrieve all active user groups with pagination")
    public ResponseEntity<Page<UserGroup>> getAllGroups(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<UserGroup> groups = userGroupService.findAllActiveGroups(pageable);
        return ResponseEntity.ok(groups);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Get group by ID", description = "Retrieve a specific user group by ID")
    public ResponseEntity<UserGroup> getGroupById(@PathVariable Long id) {
        Optional<UserGroup> group = userGroupService.findById(id);
        return group.map(ResponseEntity::ok)
                   .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Search groups", description = "Search user groups by name or description")
    public ResponseEntity<Page<UserGroup>> searchGroups(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<UserGroup> groups = userGroupService.searchGroups(query, pageable);
        return ResponseEntity.ok(groups);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Create group", description = "Create a new user group")
    public ResponseEntity<?> createGroup(@Valid @RequestBody CreateUserGroupRequest request) {
        try {
            UserGroup group = userGroupService.createUserGroup(request);
            return ResponseEntity.ok(group);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Update group", description = "Update an existing user group")
    public ResponseEntity<?> updateGroup(@PathVariable Long id, @Valid @RequestBody CreateUserGroupRequest request) {
        try {
            UserGroup group = userGroupService.updateUserGroup(id, request);
            return ResponseEntity.ok(group);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/deactivate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Deactivate group", description = "Deactivate a user group")
    public ResponseEntity<?> deactivateGroup(@PathVariable Long id) {
        try {
            userGroupService.deactivateUserGroup(id);
            return ResponseEntity.ok(new MessageResponse("Group deactivated successfully!"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/activate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Activate group", description = "Activate a user group")
    public ResponseEntity<?> activateGroup(@PathVariable Long id) {
        try {
            userGroupService.activateUserGroup(id);
            return ResponseEntity.ok(new MessageResponse("Group activated successfully!"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Delete group", description = "Permanently delete a user group")
    public ResponseEntity<?> deleteGroup(@PathVariable Long id) {
        try {
            userGroupService.deleteUserGroup(id);
            return ResponseEntity.ok(new MessageResponse("Group deleted successfully!"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PostMapping("/{groupId}/users/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Add user to group", description = "Add a user to a user group")
    public ResponseEntity<?> addUserToGroup(@PathVariable Long groupId, @PathVariable Long userId) {
        try {
            UserGroup group = userGroupService.addUserToGroup(groupId, userId);
            return ResponseEntity.ok(new MessageResponse("User added to group successfully!"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{groupId}/users/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Remove user from group", description = "Remove a user from a user group")
    public ResponseEntity<?> removeUserFromGroup(@PathVariable Long groupId, @PathVariable Long userId) {
        try {
            UserGroup group = userGroupService.removeUserFromGroup(groupId, userId);
            return ResponseEntity.ok(new MessageResponse("User removed from group successfully!"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Get groups by user", description = "Retrieve groups that a user belongs to")
    public ResponseEntity<List<UserGroup>> getGroupsByUser(@PathVariable Long userId) {
        List<UserGroup> groups = userGroupService.findGroupsByUserId(userId);
        return ResponseEntity.ok(groups);
    }

    @GetMapping("/company/{companyId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('GROUP_MANAGER') or hasRole('COMPANY_MANAGER')")
    @Operation(summary = "Get groups by company", description = "Retrieve groups associated with a company")
    public ResponseEntity<List<UserGroup>> getGroupsByCompany(@PathVariable Long companyId) {
        List<UserGroup> groups = userGroupService.findGroupsByCompanyId(companyId);
        return ResponseEntity.ok(groups);
    }
}
