package com.example.springbootapi.controller;

import com.example.springbootapi.dto.request.CreateCompanyRequest;
import com.example.springbootapi.dto.response.MessageResponse;
import com.example.springbootapi.model.Company;
import com.example.springbootapi.service.CompanyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/companies")
@Tag(name = "Company Management", description = "Company management APIs")
@SecurityRequirement(name = "bearerAuth")
public class CompanyController {

    @Autowired
    private CompanyService companyService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Get all companies", description = "Retrieve all active companies with pagination")
    public ResponseEntity<Page<Company>> getAllCompanies(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Company> companies = companyService.findAllActiveCompanies(pageable);
        return ResponseEntity.ok(companies);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Get company by ID", description = "Retrieve a specific company by ID")
    public ResponseEntity<Company> getCompanyById(@PathVariable Long id) {
        Optional<Company> company = companyService.findById(id);
        return company.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Search companies", description = "Search companies by name, description, industry, or registration number")
    public ResponseEntity<Page<Company>> searchCompanies(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Company> companies = companyService.searchCompanies(query, pageable);
        return ResponseEntity.ok(companies);
    }

    @GetMapping("/industry/{industry}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Get companies by industry", description = "Retrieve companies by industry")
    public ResponseEntity<Page<Company>> getCompaniesByIndustry(
            @PathVariable String industry,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Company> companies = companyService.findCompaniesByIndustry(industry, pageable);
        return ResponseEntity.ok(companies);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER')")
    @Operation(summary = "Create company", description = "Create a new company")
    public ResponseEntity<?> createCompany(@Valid @RequestBody CreateCompanyRequest request) {
        try {
            Company company = companyService.createCompany(request);
            return ResponseEntity.ok(company);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER')")
    @Operation(summary = "Update company", description = "Update an existing company")
    public ResponseEntity<?> updateCompany(@PathVariable Long id, @Valid @RequestBody CreateCompanyRequest request) {
        try {
            Company company = companyService.updateCompany(id, request);
            return ResponseEntity.ok(company);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/deactivate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER')")
    @Operation(summary = "Deactivate company", description = "Deactivate a company")
    public ResponseEntity<?> deactivateCompany(@PathVariable Long id) {
        try {
            companyService.deactivateCompany(id);
            return ResponseEntity.ok(new MessageResponse("Company deactivated successfully!"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/activate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER')")
    @Operation(summary = "Activate company", description = "Activate a company")
    public ResponseEntity<?> activateCompany(@PathVariable Long id) {
        try {
            companyService.activateCompany(id);
            return ResponseEntity.ok(new MessageResponse("Company activated successfully!"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Delete company", description = "Permanently delete a company")
    public ResponseEntity<?> deleteCompany(@PathVariable Long id) {
        try {
            companyService.deleteCompany(id);
            return ResponseEntity.ok(new MessageResponse("Company deleted successfully!"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PostMapping("/{companyId}/groups/{groupId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Associate group with company", description = "Associate a user group with a company")
    public ResponseEntity<?> addGroupToCompany(@PathVariable Long companyId, @PathVariable Long groupId) {
        try {
            Company company = companyService.addUserGroupToCompany(companyId, groupId);
            return ResponseEntity.ok(new MessageResponse("Group associated with company successfully!"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{companyId}/groups/{groupId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Remove group from company", description = "Remove a user group association from a company")
    public ResponseEntity<?> removeGroupFromCompany(@PathVariable Long companyId, @PathVariable Long groupId) {
        try {
            Company company = companyService.removeUserGroupFromCompany(companyId, groupId);
            return ResponseEntity.ok(new MessageResponse("Group removed from company successfully!"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @GetMapping("/group/{groupId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_MANAGER') or hasRole('GROUP_MANAGER')")
    @Operation(summary = "Get companies by group", description = "Retrieve companies associated with a user group")
    public ResponseEntity<List<Company>> getCompaniesByGroup(@PathVariable Long groupId) {
        List<Company> companies = companyService.findCompaniesByGroupId(groupId);
        return ResponseEntity.ok(companies);
    }
}
