package com.example.springbootapi.model;

import com.example.springbootapi.model.enums.Role;
import com.example.springbootapi.model.enums.UserType;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@DiscriminatorValue("AGENT")
public class AgentUser extends User {

    @Size(max = 20)
    @Column(name = "agent_id", unique = true)
    private String agentId;

    @Size(max = 100)
    @Column(name = "agency_name")
    private String agencyName;

    @Column(name = "license_number")
    private String licenseNumber;

    @Column(name = "license_expiry_date")
    private LocalDate licenseExpiryDate;

    @Column(name = "commission_rate", precision = 5, scale = 2)
    private BigDecimal commissionRate;

    @Size(max = 100)
    @Column(name = "specialization")
    private String specialization;

    // Constructors
    public AgentUser() {
        super();
        setUserType(UserType.AGENT);
        getRoles().add(Role.ROLE_AGENT_USER);
    }

    public AgentUser(String username, String email, String password, String firstName, String lastName,
                    String agentId, String agencyName, String licenseNumber, LocalDate licenseExpiryDate) {
        super(username, email, password, firstName, lastName);
        this.agentId = agentId;
        this.agencyName = agencyName;
        this.licenseNumber = licenseNumber;
        this.licenseExpiryDate = licenseExpiryDate;
        setUserType(UserType.AGENT);
        getRoles().add(Role.ROLE_AGENT_USER);
    }

    // Getters and Setters
    public String getAgentId() { return agentId; }
    public void setAgentId(String agentId) { this.agentId = agentId; }

    public String getAgencyName() { return agencyName; }
    public void setAgencyName(String agencyName) { this.agencyName = agencyName; }

    public String getLicenseNumber() { return licenseNumber; }
    public void setLicenseNumber(String licenseNumber) { this.licenseNumber = licenseNumber; }

    public LocalDate getLicenseExpiryDate() { return licenseExpiryDate; }
    public void setLicenseExpiryDate(LocalDate licenseExpiryDate) { this.licenseExpiryDate = licenseExpiryDate; }

    public BigDecimal getCommissionRate() { return commissionRate; }
    public void setCommissionRate(BigDecimal commissionRate) { this.commissionRate = commissionRate; }

    public String getSpecialization() { return specialization; }
    public void setSpecialization(String specialization) { this.specialization = specialization; }
}
