package com.example.springbootapi.model;

import com.example.springbootapi.model.enums.Role;
import com.example.springbootapi.model.enums.UserType;
import javax.persistence.*;
import javax.validation.constraints.Size;

import java.util.Set;

@Entity
@DiscriminatorValue("CORPORATE")
public class CorporateUser extends User {

    @Size(max = 100)
    @Column(name = "company_name")
    private String companyName;

    @Size(max = 50)
    @Column(name = "department")
    private String department;

    @Size(max = 50)
    @Column(name = "job_title")
    private String jobTitle;

    @Size(max = 20)
    @Column(name = "employee_id")
    private String employeeId;

    // Constructors
    public CorporateUser() {
        super();
        setUserType(UserType.CORPORATE);
        getRoles().add(Role.ROLE_CORPORATE_USER);
    }

    public CorporateUser(String username, String email, String password, String firstName, String lastName,
                        String companyName, String department, String jobTitle, String employeeId) {
        super(username, email, password, firstName, lastName);
        this.companyName = companyName;
        this.department = department;
        this.jobTitle = jobTitle;
        this.employeeId = employeeId;
        setUserType(UserType.CORPORATE);
        getRoles().add(Role.ROLE_CORPORATE_USER);
    }

    // Getters and Setters
    public String getCompanyName() { return companyName; }
    public void setCompanyName(String companyName) { this.companyName = companyName; }

    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }

    public String getJobTitle() { return jobTitle; }
    public void setJobTitle(String jobTitle) { this.jobTitle = jobTitle; }

    public String getEmployeeId() { return employeeId; }
    public void setEmployeeId(String employeeId) { this.employeeId = employeeId; }
}
