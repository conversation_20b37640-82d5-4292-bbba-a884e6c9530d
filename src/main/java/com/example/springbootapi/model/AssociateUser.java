package com.example.springbootapi.model;

import com.example.springbootapi.model.enums.Role;
import com.example.springbootapi.model.enums.UserType;
import javax.persistence.*;
import javax.validation.constraints.Size;

import java.time.LocalDate;

@Entity
@DiscriminatorValue("ASSOCIATE")
public class AssociateUser extends User {

    @Size(max = 20)
    @Column(name = "associate_id", unique = true)
    private String associateId;

    @Column(name = "supervisor_id")
    private Long supervisorId;

    @Size(max = 100)
    @Column(name = "certification")
    private String certification;

    @Column(name = "certification_expiry_date")
    private LocalDate certificationExpiryDate;

    @Size(max = 50)
    @Column(name = "level")
    private String level;

    @Column(name = "hire_date")
    private LocalDate hireDate;

    @Size(max = 100)
    @Column(name = "training_status")
    private String trainingStatus;

    // Constructors
    public AssociateUser() {
        super();
        setUserType(UserType.ASSOCIATE);
        getRoles().add(Role.ROLE_ASSOCIATE_USER);
    }

    public AssociateUser(String username, String email, String password, String firstName, String lastName,
                        String associateId, Long supervisorId, String certification, LocalDate hireDate) {
        super(username, email, password, firstName, lastName);
        this.associateId = associateId;
        this.supervisorId = supervisorId;
        this.certification = certification;
        this.hireDate = hireDate;
        setUserType(UserType.ASSOCIATE);
        getRoles().add(Role.ROLE_ASSOCIATE_USER);
    }

    // Getters and Setters
    public String getAssociateId() { return associateId; }
    public void setAssociateId(String associateId) { this.associateId = associateId; }

    public Long getSupervisorId() { return supervisorId; }
    public void setSupervisorId(Long supervisorId) { this.supervisorId = supervisorId; }

    public String getCertification() { return certification; }
    public void setCertification(String certification) { this.certification = certification; }

    public LocalDate getCertificationExpiryDate() { return certificationExpiryDate; }
    public void setCertificationExpiryDate(LocalDate certificationExpiryDate) { this.certificationExpiryDate = certificationExpiryDate; }

    public String getLevel() { return level; }
    public void setLevel(String level) { this.level = level; }

    public LocalDate getHireDate() { return hireDate; }
    public void setHireDate(LocalDate hireDate) { this.hireDate = hireDate; }

    public String getTrainingStatus() { return trainingStatus; }
    public void setTrainingStatus(String trainingStatus) { this.trainingStatus = trainingStatus; }
}
