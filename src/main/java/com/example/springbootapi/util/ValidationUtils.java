package com.example.springbootapi.util;

import java.util.regex.Pattern;

public class ValidationUtils {
    
    // Password validation: at least 8 characters, contains letters and numbers
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
        "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d@$!%*#?&]{8,}$"
    );
    
    // Email validation (basic)
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$"
    );
    
    // Username validation: alphanumeric and underscore, 3-20 characters
    private static final Pattern USERNAME_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_]{3,20}$"
    );
    
    // Phone number validation (international format)
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^\\+?[1-9]\\d{1,14}$"
    );
    
    // SQL injection prevention patterns
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript)",
        Pattern.CASE_INSENSITIVE
    );
    
    // XSS prevention patterns
    private static final Pattern XSS_PATTERN = Pattern.compile(
        "(?i)<script[^>]*>.*?</script>|javascript:|vbscript:|onload=|onerror=|onclick=",
        Pattern.CASE_INSENSITIVE
    );

    public static boolean isValidPassword(String password) {
        return password != null && PASSWORD_PATTERN.matcher(password).matches();
    }

    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    public static boolean isValidUsername(String username) {
        return username != null && USERNAME_PATTERN.matcher(username).matches();
    }

    public static boolean isValidPhoneNumber(String phoneNumber) {
        return phoneNumber == null || phoneNumber.isEmpty() || 
               PHONE_PATTERN.matcher(phoneNumber).matches();
    }

    public static boolean containsSqlInjection(String input) {
        return input != null && SQL_INJECTION_PATTERN.matcher(input).find();
    }

    public static boolean containsXss(String input) {
        return input != null && XSS_PATTERN.matcher(input).find();
    }

    public static String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }
        
        // Remove potential XSS and SQL injection patterns
        String sanitized = input.replaceAll("(?i)<script[^>]*>.*?</script>", "")
                               .replaceAll("(?i)javascript:", "")
                               .replaceAll("(?i)vbscript:", "")
                               .replaceAll("(?i)on\\w+\\s*=", "")
                               .replaceAll("'", "''") // Escape single quotes
                               .trim();
        
        return sanitized;
    }

    public static boolean isValidSearchTerm(String searchTerm) {
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return false;
        }
        
        // Check for malicious patterns
        if (containsSqlInjection(searchTerm) || containsXss(searchTerm)) {
            return false;
        }
        
        // Check length (reasonable search term length)
        return searchTerm.length() <= 100;
    }

    public static String getPasswordStrengthMessage(String password) {
        if (password == null || password.length() < 8) {
            return "Password must be at least 8 characters long";
        }
        
        if (!password.matches(".*[A-Za-z].*")) {
            return "Password must contain at least one letter";
        }
        
        if (!password.matches(".*\\d.*")) {
            return "Password must contain at least one number";
        }
        
        if (password.length() < 12) {
            return "Consider using a longer password for better security";
        }
        
        return "Strong password";
    }
}
