/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/dto/request/LoginRequest.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/dto/request/CreateCompanyRequest.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/exception/GlobalExceptionHandler.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/SpringBootApiApplication.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/service/CompanyService.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/dto/request/CreateUserGroupRequest.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/repository/UserRepository.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/controller/UserGroupController.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/dto/request/CreateUserRequest.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/model/AssociateUser.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/security/AuthEntryPointJwt.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/model/User.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/model/AgentUser.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/service/UserGroupService.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/security/WebSecurityConfig.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/controller/CompanyController.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/security/AuthTokenFilter.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/security/UserDetailsImpl.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/model/enums/UserType.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/config/AuditConfig.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/config/OpenApiConfig.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/config/DataInitializer.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/security/JwtUtils.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/service/UserService.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/controller/AuthController.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/model/enums/Role.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/repository/CompanyRepository.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/dto/response/JwtResponse.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/model/UserGroup.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/controller/UserController.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/model/Company.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/security/UserDetailsServiceImpl.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/model/CorporateUser.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/repository/UserGroupRepository.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/config/SecurityHeadersConfig.java
/Users/<USER>/Development/spring-boot-api/src/main/java/com/example/springbootapi/dto/response/MessageResponse.java
