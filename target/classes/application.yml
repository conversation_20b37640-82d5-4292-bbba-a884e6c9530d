spring:
  application:
    name: spring-boot-api
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: true
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

server:
  port: 8080
  servlet:
    context-path: /api
  error:
    include-message: always
    include-binding-errors: always

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

logging:
  level:
    com.example.springbootapi: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG

app:
  jwt:
    secret: mySecretKey123456789012345678901234567890
    expiration: 86400000 # 24 hours in milliseconds
  security:
    cors:
      allowed-origins: http://localhost:3000,http://localhost:8080
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true
