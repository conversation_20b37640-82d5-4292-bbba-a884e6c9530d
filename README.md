# Spring Boot API - User and Company Management System

A secure Spring Boot REST API for managing users, user groups, and companies with comprehensive authentication and authorization.

## Features

### User Management
- **Three User Types**: Corporate, Agent, and Associate users with specific attributes
- **CRUD Operations**: Create, read, update, delete, and search users
- **User Authentication**: JWT-based authentication with role-based access control
- **User Activation/Deactivation**: Soft delete functionality

### User Groups
- **Group Management**: Create, update, delete, and search user groups
- **User-Group Association**: Add/remove users from groups
- **Group Authorization**: Role-based access to group operations

### Company Management
- **Company CRUD**: Full company lifecycle management
- **Industry Filtering**: Search companies by industry
- **Company-Group Association**: Link user groups to companies

### Security Features
- **JWT Authentication**: Secure token-based authentication
- **Role-Based Authorization**: Fine-grained access control
- **Password Encryption**: BCrypt password hashing
- **Security Headers**: Comprehensive security headers (CSP, HSTS, etc.)
- **Input Validation**: Request validation and sanitization
- **Audit Logging**: Track entity changes with timestamps

## Technology Stack

- **Framework**: Spring Boot 3.2.0
- **Security**: Spring Security 6 with JWT
- **Database**: H2 (development), JPA/Hibernate
- **Documentation**: OpenAPI 3 (Swagger)
- **Testing**: JUnit 5, Mockito, Spring Boot Test
- **Build Tool**: Maven

## Getting Started

### Prerequisites
- Java 17 or higher
- Maven 3.6 or higher

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd spring-boot-api
   ```

2. **Build the application**
   ```bash
   mvn clean install
   ```

3. **Run the application**
   ```bash
   mvn spring-boot:run
   ```

4. **Access the application**
   - API Base URL: `http://localhost:8080/api`
   - Swagger UI: `http://localhost:8080/api/swagger-ui.html`
   - H2 Console: `http://localhost:8080/api/h2-console`

## API Documentation

### Authentication Endpoints

#### Sign Up
```http
POST /auth/signup
Content-Type: application/json

{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "userType": "CORPORATE",
  "companyName": "Tech Corp",
  "department": "IT",
  "jobTitle": "Developer",
  "employeeId": "EMP001"
}
```

#### Sign In
```http
POST /auth/signin
Content-Type: application/json

{
  "username": "john_doe",
  "password": "password123"
}
```

### User Management Endpoints

#### Get All Users
```http
GET /users?page=0&size=10&sortBy=id&sortDir=asc
Authorization: Bearer <jwt-token>
```

#### Create User
```http
POST /users
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "username": "agent_smith",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Agent",
  "lastName": "Smith",
  "userType": "AGENT",
  "agentId": "AGT001",
  "agencyName": "Premium Agency",
  "licenseNumber": "LIC123456"
}
```

#### Search Users
```http
GET /users/search?query=john&page=0&size=10
Authorization: Bearer <jwt-token>
```

### User Group Endpoints

#### Create Group
```http
POST /groups
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "name": "Sales Team",
  "description": "Sales department group"
}
```

#### Add User to Group
```http
POST /groups/{groupId}/users/{userId}
Authorization: Bearer <jwt-token>
```

### Company Endpoints

#### Create Company
```http
POST /companies
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "name": "Tech Solutions Inc",
  "description": "Leading technology solutions provider",
  "registrationNumber": "REG001",
  "industry": "Technology",
  "city": "New York",
  "state": "NY",
  "country": "USA",
  "email": "<EMAIL>"
}
```

## Default Users

The application creates default users on startup:

| Username | Password | Role | Type |
|----------|----------|------|------|
| admin | admin123 | ADMIN | Corporate |
| groupmgr | password123 | GROUP_MANAGER | Corporate |
| companymgr | password123 | COMPANY_MANAGER | Corporate |
| agent1 | password123 | AGENT_USER | Agent |
| associate1 | password123 | ASSOCIATE_USER | Associate |

## Security Configuration

### Roles and Permissions

- **ADMIN**: Full access to all resources
- **GROUP_MANAGER**: Manage user groups and view users
- **COMPANY_MANAGER**: Manage companies and associated groups
- **CORPORATE_USER**: Basic corporate user access
- **AGENT_USER**: Agent-specific access
- **ASSOCIATE_USER**: Associate-specific access

### Security Headers

The application implements comprehensive security headers:
- Content Security Policy (CSP)
- X-Content-Type-Options
- X-Frame-Options
- X-XSS-Protection
- Strict Transport Security (HSTS)
- Referrer Policy

## Testing

### Run Tests
```bash
mvn test
```

### Test Coverage
- Unit tests for services and utilities
- Integration tests for controllers
- Security tests for authentication and authorization

## Configuration

### Application Properties
Key configuration options in `application.yml`:

```yaml
app:
  jwt:
    secret: mySecretKey123456789012345678901234567890
    expiration: 86400000 # 24 hours
  security:
    cors:
      allowed-origins: http://localhost:3000,http://localhost:8080
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
```

## Database Schema

### User Hierarchy
- `User` (base entity)
  - `CorporateUser` (company-specific fields)
  - `AgentUser` (agent-specific fields)
  - `AssociateUser` (associate-specific fields)

### Relationships
- Users ↔ UserGroups (Many-to-Many)
- UserGroups ↔ Companies (Many-to-Many)

## Project Structure

```
spring-boot-api/
├── src/
│   ├── main/
│   │   ├── java/com/example/springbootapi/
│   │   │   ├── config/          # Configuration classes
│   │   │   ├── controller/      # REST controllers
│   │   │   ├── dto/            # Data transfer objects
│   │   │   ├── exception/      # Exception handlers
│   │   │   ├── model/          # Entity classes
│   │   │   ├── repository/     # Data repositories
│   │   │   ├── security/       # Security components
│   │   │   ├── service/        # Business logic
│   │   │   └── util/           # Utility classes
│   │   └── resources/
│   │       ├── application.yml      # Main configuration
│   │       ├── application-prod.yml # Production configuration
│   │       └── application-test.yml # Test configuration
│   └── test/                   # Test classes
├── docs/
│   ├── API_DOCUMENTATION.md    # Complete API documentation
│   ├── DEPLOYMENT_GUIDE.md     # Deployment instructions
│   └── SECURITY_CHECKLIST.md   # Security guidelines
├── docker-compose.yml          # Docker composition
├── Dockerfile                  # Container definition
└── pom.xml                    # Maven configuration
```

## Quick Start

1. **Clone and run locally:**
   ```bash
   git clone <repository-url>
   cd spring-boot-api
   mvn spring-boot:run
   ```

2. **Access the application:**
   - API: http://localhost:8080/api
   - Swagger UI: http://localhost:8080/api/swagger-ui.html
   - H2 Console: http://localhost:8080/api/h2-console

3. **Test with default admin user:**
   ```bash
   curl -X POST http://localhost:8080/api/auth/signin \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123"}'
   ```

## Documentation

- **[Complete API Documentation](API_DOCUMENTATION.md)** - Detailed API reference
- **[Deployment Guide](DEPLOYMENT_GUIDE.md)** - Production deployment instructions
- **[Security Checklist](SECURITY_CHECKLIST.md)** - Security best practices and compliance

## Production Deployment

### Docker
```bash
docker-compose up -d
```

### Kubernetes
```bash
kubectl apply -f k8s/
```

### Cloud Platforms
- AWS ECS/EKS
- Google Cloud Run/GKE
- Azure Container Instances/AKS

## Monitoring and Observability

- **Health Checks**: `/actuator/health`
- **Metrics**: `/actuator/metrics`
- **Prometheus**: `/actuator/prometheus`
- **Request Logging**: Structured JSON logs
- **Security Events**: Audit trail logging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License.
