# Spring Boot API - Complete API Documentation

## Overview

This is a comprehensive REST API for managing users, user groups, and companies with enterprise-grade security features.

## Base URL
```
http://localhost:8080/api
```

## Authentication

All endpoints except authentication endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <jwt-token>
```

## Security Features

### 1. JWT Authentication
- Secure token-based authentication
- Configurable token expiration
- Automatic token validation

### 2. Role-Based Authorization
- **ADMIN**: Full system access
- **GROUP_MANAGER**: Manage user groups and view users
- **COMPANY_MANAGER**: Manage companies and associated groups
- **CORPORATE_USER**: Basic corporate user access
- **AGENT_USER**: Agent-specific access
- **ASSOCIATE_USER**: Associate-specific access

### 3. Security Headers
- Content Security Policy (CSP)
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Strict Transport Security (HSTS)
- Referrer Policy: strict-origin-when-cross-origin

### 4. Rate Limiting
- 100 requests per minute per IP address
- Automatic cleanup of old entries
- Configurable limits

### 5. Input Validation
- SQL injection prevention
- XSS protection
- Input sanitization
- Password strength validation

## API Endpoints

### Authentication Endpoints

#### POST /auth/signup
Register a new user account.

**Request Body:**
```json
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "userType": "CORPORATE|AGENT|ASSOCIATE",
  "phoneNumber": "+**********",
  
  // Corporate User Fields
  "companyName": "Tech Corp",
  "department": "IT",
  "jobTitle": "Developer",
  "employeeId": "EMP001",
  
  // Agent User Fields
  "agentId": "AGT001",
  "agencyName": "Premium Agency",
  "licenseNumber": "LIC123456",
  "licenseExpiryDate": "2025-12-31",
  "commissionRate": 0.05,
  "specialization": "Technology Sales",
  
  // Associate User Fields
  "associateId": "ASC001",
  "supervisorId": 1,
  "certification": "Basic Certification",
  "certificationExpiryDate": "2025-12-31",
  "level": "Junior",
  "hireDate": "2024-01-15",
  "trainingStatus": "Completed"
}
```

**Response:**
```json
{
  "message": "User registered successfully!"
}
```

#### POST /auth/signin
Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "username": "john_doe",
  "password": "password123"
}
```

**Response:**
```json
{
  "id": 1,
  "username": "john_doe",
  "email": "<EMAIL>",
  "roles": ["ROLE_CORPORATE_USER"],
  "tokenType": "Bearer",
  "accessToken": "eyJhbGciOiJIUzI1NiJ9..."
}
```

### User Management Endpoints

#### GET /users
Retrieve all active users with pagination.

**Query Parameters:**
- `page` (default: 0): Page number
- `size` (default: 10): Page size
- `sortBy` (default: id): Sort field
- `sortDir` (default: asc): Sort direction

**Required Role:** ADMIN, GROUP_MANAGER

**Response:**
```json
{
  "content": [
    {
      "id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phoneNumber": "+**********",
      "active": true,
      "userType": "CORPORATE",
      "roles": ["ROLE_CORPORATE_USER"],
      "createdAt": "2024-01-15T10:30:00",
      "updatedAt": "2024-01-15T10:30:00",
      // Type-specific fields based on userType
      "companyName": "Tech Corp",
      "department": "IT",
      "jobTitle": "Developer",
      "employeeId": "EMP001"
    }
  ],
  "pageable": {
    "sort": {"sorted": true, "unsorted": false},
    "pageNumber": 0,
    "pageSize": 10
  },
  "totalElements": 1,
  "totalPages": 1,
  "first": true,
  "last": true
}
```

#### GET /users/{id}
Retrieve a specific user by ID.

**Required Role:** ADMIN, GROUP_MANAGER, or own user

#### GET /users/search
Search users by name, username, or email.

**Query Parameters:**
- `query`: Search term
- `page`, `size`, `sortBy`, `sortDir`: Pagination parameters

**Required Role:** ADMIN, GROUP_MANAGER

#### GET /users/type/{userType}
Retrieve users by type (CORPORATE, AGENT, ASSOCIATE).

**Required Role:** ADMIN, GROUP_MANAGER

#### POST /users
Create a new user.

**Required Role:** ADMIN

**Request Body:** Same as signup request

#### PUT /users/{id}
Update an existing user.

**Required Role:** ADMIN or own user

#### PUT /users/{id}/deactivate
Deactivate a user account.

**Required Role:** ADMIN

#### PUT /users/{id}/activate
Activate a user account.

**Required Role:** ADMIN

#### DELETE /users/{id}
Permanently delete a user.

**Required Role:** ADMIN

#### GET /users/group/{groupId}
Get users belonging to a specific group.

**Required Role:** ADMIN, GROUP_MANAGER

### User Group Management Endpoints

#### GET /groups
Retrieve all active user groups with pagination.

**Required Role:** ADMIN, GROUP_MANAGER

#### GET /groups/{id}
Retrieve a specific user group by ID.

**Required Role:** ADMIN, GROUP_MANAGER

#### GET /groups/search
Search user groups by name or description.

**Query Parameters:**
- `query`: Search term
- Pagination parameters

**Required Role:** ADMIN, GROUP_MANAGER

#### POST /groups
Create a new user group.

**Required Role:** ADMIN, GROUP_MANAGER

**Request Body:**
```json
{
  "name": "Sales Team",
  "description": "Sales department group"
}
```

#### PUT /groups/{id}
Update an existing user group.

**Required Role:** ADMIN, GROUP_MANAGER

#### PUT /groups/{id}/deactivate
Deactivate a user group.

**Required Role:** ADMIN, GROUP_MANAGER

#### PUT /groups/{id}/activate
Activate a user group.

**Required Role:** ADMIN, GROUP_MANAGER

#### DELETE /groups/{id}
Permanently delete a user group.

**Required Role:** ADMIN

#### POST /groups/{groupId}/users/{userId}
Add a user to a user group.

**Required Role:** ADMIN, GROUP_MANAGER

#### DELETE /groups/{groupId}/users/{userId}
Remove a user from a user group.

**Required Role:** ADMIN, GROUP_MANAGER

#### GET /groups/user/{userId}
Get groups that a user belongs to.

**Required Role:** ADMIN, GROUP_MANAGER

#### GET /groups/company/{companyId}
Get groups associated with a company.

**Required Role:** ADMIN, GROUP_MANAGER, COMPANY_MANAGER

### Company Management Endpoints

#### GET /companies
Retrieve all active companies with pagination.

**Required Role:** ADMIN, COMPANY_MANAGER, GROUP_MANAGER

#### GET /companies/{id}
Retrieve a specific company by ID.

**Required Role:** ADMIN, COMPANY_MANAGER, GROUP_MANAGER

#### GET /companies/search
Search companies by name, description, industry, or registration number.

**Required Role:** ADMIN, COMPANY_MANAGER, GROUP_MANAGER

#### GET /companies/industry/{industry}
Retrieve companies by industry.

**Required Role:** ADMIN, COMPANY_MANAGER, GROUP_MANAGER

#### POST /companies
Create a new company.

**Required Role:** ADMIN, COMPANY_MANAGER

**Request Body:**
```json
{
  "name": "Tech Solutions Inc",
  "description": "Leading technology solutions provider",
  "registrationNumber": "REG001",
  "industry": "Technology",
  "address": "123 Tech Street",
  "city": "New York",
  "state": "NY",
  "postalCode": "10001",
  "country": "USA",
  "phoneNumber": "+**********",
  "email": "<EMAIL>",
  "website": "https://techsolutions.com"
}
```

#### PUT /companies/{id}
Update an existing company.

**Required Role:** ADMIN, COMPANY_MANAGER

#### PUT /companies/{id}/deactivate
Deactivate a company.

**Required Role:** ADMIN, COMPANY_MANAGER

#### PUT /companies/{id}/activate
Activate a company.

**Required Role:** ADMIN, COMPANY_MANAGER

#### DELETE /companies/{id}
Permanently delete a company.

**Required Role:** ADMIN

#### POST /companies/{companyId}/groups/{groupId}
Associate a user group with a company.

**Required Role:** ADMIN, COMPANY_MANAGER, GROUP_MANAGER

#### DELETE /companies/{companyId}/groups/{groupId}
Remove a user group association from a company.

**Required Role:** ADMIN, COMPANY_MANAGER, GROUP_MANAGER

#### GET /companies/group/{groupId}
Get companies associated with a user group.

**Required Role:** ADMIN, COMPANY_MANAGER, GROUP_MANAGER

## Error Responses

### Standard Error Format
```json
{
  "timestamp": "2024-01-15T10:30:00",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed",
  "path": "/api/users"
}
```

### Common HTTP Status Codes
- **200 OK**: Successful request
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource already exists
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

## Rate Limiting

The API implements rate limiting to prevent abuse:
- **Limit**: 100 requests per minute per IP address
- **Response**: HTTP 429 when limit exceeded
- **Headers**: Rate limit information in response headers

## Logging and Monitoring

### Request Logging
All requests are logged with:
- Request ID for tracking
- Client IP address
- User agent
- Request method and URI
- Response status and duration

### Health Checks
- **Endpoint**: `/actuator/health`
- **Purpose**: Application health monitoring
- **Response**: JSON with application status

### Metrics
- **Endpoint**: `/actuator/metrics`
- **Purpose**: Application metrics
- **Format**: Prometheus-compatible metrics

## Security Best Practices

1. **Always use HTTPS in production**
2. **Rotate JWT secrets regularly**
3. **Implement proper CORS policies**
4. **Monitor for suspicious activity**
5. **Keep dependencies updated**
6. **Use strong passwords**
7. **Implement proper logging**
8. **Regular security audits**

## Development vs Production

### Development
- H2 in-memory database
- Detailed error messages
- Debug logging enabled
- Relaxed CORS policies

### Production
- PostgreSQL database
- Minimal error exposure
- Info-level logging
- Strict CORS policies
- Rate limiting enabled
- Security headers enforced
