version: '3.8'

services:
  spring-boot-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=jdbc:h2:mem:testdb
      - SPRING_DATASOURCE_USERNAME=sa
      - SPRING_DATASOURCE_PASSWORD=password
      - APP_JWT_SECRET=dockerSecretKey123456789012345678901234567890
      - APP_JWT_EXPIRATION=86400000
    volumes:
      - ./logs:/app/logs
    networks:
      - spring-boot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add PostgreSQL for production use
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: springbootapi
  #     POSTGRES_USER: apiuser
  #     POSTGRES_PASSWORD: apipassword
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - spring-boot-network
  #   restart: unless-stopped

networks:
  spring-boot-network:
    driver: bridge

volumes:
  postgres_data:
